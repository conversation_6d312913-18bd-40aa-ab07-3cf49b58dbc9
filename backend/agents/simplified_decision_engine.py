#!/usr/bin/env python3
"""
简化决策引擎

基于统一配置和状态机的决策引擎实现，提供：
- 三层意图识别（关键词匹配、语义匹配、LLM识别）
- 状态感知的决策逻辑
- 统一配置驱动的决策规则
- 高性能的关键词加速器

主要功能：
1. 意图识别和分类
2. 基于状态的决策路由
3. LLM回退机制
4. 决策结果缓存
"""

import logging
import json
import re
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from backend.config.unified_config_loader import get_unified_config
from backend.config.keywords_loader import get_keywords_loader
from backend.agents.unified_state_manager import ConversationState
from backend.agents.llm_interface import LLMServiceInterface, PromptLoaderInterface
from backend.utils.intent_manager import IntentManager


# 定义决策结果数据类
@dataclass
class DecisionResult:
    """决策结果"""
    action: str
    priority: int
    response_template: str
    next_state: Optional[ConversationState] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


# 定义动作类型枚举
class ActionType(Enum):
    """动作类型枚举"""
    SEND_GREETING = "send_greeting" # 发送问候
    START_REQUIREMENT_COLLECTION = "start_requirement_collection" # 开始需求收集
    COLLECT_INFORMATION = "collect_information" # 信息收集
    START_DOCUMENT_GENERATION = "start_document_generation" # 开始文档生成
    CONFIRM_DOCUMENT = "confirm_document" # 确认文档
    MODIFY_DOCUMENT = "modify_document" # 修改文档
    COMPLETE_CONVERSATION = "complete_conversation" # 完成对话
    RESTART_CONVERSATION = "restart_conversation" # 重新开始对话
    SHOW_CAPABILITIES = "show_capabilities" # 展示能力
    HANDLE_UNKNOWN = "handle_unknown" # 处理未知意图

# 简化决策引擎
class SimplifiedDecisionEngine:
    """简化决策引擎"""
    
    def __init__(self, llm_service: LLMServiceInterface = None, prompt_loader: PromptLoaderInterface = None):
        self.logger = logging.getLogger(__name__)
        self.config = get_unified_config()
        
        # 通过依赖注入获取服务，如果未提供则使用默认方式获取
        if llm_service:
            self.llm_service = llm_service
        else:
            from backend.agents.factory import agent_factory
            self.llm_service = agent_factory.get_llm_service()
            
        if prompt_loader:
            self.prompt_loader = prompt_loader
        else:
            from backend.utils.prompt_loader import PromptLoader
            self.prompt_loader = PromptLoader()

        # 🔄 [意图统一化] 初始化意图管理器
        try:
            self.intent_manager = IntentManager()
            self.logger.info("意图管理器初始化成功")
            # 启动时配置完整性检查
            self._validate_intent_configuration()
        except Exception as e:
            self.logger.error(f"意图管理器初始化失败: {e}")
            # 如果配置加载失败，使用备用模式
            self.intent_manager = None
            self.logger.warning("将使用备用的硬编码意图列表")

        # 🔧 [关键词重构] 初始化统一关键词加载器
        try:
            self.keywords_loader = get_keywords_loader()
            self.logger.info("统一关键词加载器初始化成功")
        except Exception as e:
            self.logger.error(f"关键词加载器初始化失败: {e}")
            self.keywords_loader = None
            self.logger.warning("将使用备用的硬编码关键词列表")

        # 加载决策规则
        self._decision_rules = self._load_decision_rules()
        
        # 定义意图优先级规则（顺序即优先级，从高到低）
        self.intent_priority = [
            "search_knowledge_base",      # 知识库查询 - 最高优先级
            "ask_question",               # 问题询问
            "business_requirement",       # 业务需求
            "ask_introduction",           # 自我介绍
            "ask_capabilities",           # 能力询问
            "modify",                     # 修改
            "confirm",                    # 确认
            "restart",                    # 重新开始
            "emotional_support",          # 情感支持
            "greeting",                   # 问候 - 最低优先级
            "general_chat",               # 闲聊
            "provide_information",        # 提供信息
            "unknown"                     # 未知
        ]
        
        self.logger.info("简化决策引擎初始化完成")

    def enable_acceleration_mode(self):
        """
        启用加速模式

        这个方法是为了兼容性而添加的，因为某些代码期望决策引擎有这个方法。
        SimplifiedDecisionEngine 本身已经包含了关键词加速功能，所以这个方法只是记录日志。
        """
        self.logger.info("简化决策引擎加速模式已启用（本引擎默认支持关键词加速）")

    def _validate_intent_configuration(self):
        """
        🔄 [意图统一化] 验证意图配置的完整性

        检查配置文件中的意图定义是否完整和有效
        """
        if not self.intent_manager:
            return

        try:
            # 获取配置信息
            config_info = self.intent_manager.get_config_info()
            valid_intents = self.intent_manager.get_valid_intents()

            self.logger.info(f"配置完整性检查: 发现 {len(valid_intents)} 个意图定义")

            # 检查关键意图是否存在
            required_intents = [
                "greeting", "business_requirement", "unknown",
                "general_chat", "request_clarification"
            ]

            missing_intents = []
            for intent in required_intents:
                if not self.intent_manager.is_valid_intent(intent):
                    missing_intents.append(intent)

            if missing_intents:
                self.logger.warning(f"配置检查警告: 缺少关键意图定义: {missing_intents}")
            else:
                self.logger.info("配置完整性检查通过: 所有关键意图都已定义")

            # 检查决策规则完整性
            decision_rules = self.intent_manager.get_decision_rules()
            if not decision_rules:
                self.logger.warning("配置检查警告: 缺少决策规则配置")
            else:
                self.logger.info("决策规则配置检查通过")

        except Exception as e:
            self.logger.error(f"配置完整性检查失败: {e}")

    def _clean_json_response(self, text: str) -> str:
        """清理LLM响应中的常见格式问题"""
        try:
            # 移除代码块标记
            text = re.sub(r'```json\s*', '', text, flags=re.IGNORECASE)
            text = re.sub(r'```\s*$', '', text)

            # 移除常见的前缀文字（保守策略）
            text = re.sub(r'^[^{]*(?=\{)', '', text)

            # 移除常见的后缀文字（保守策略）
            text = re.sub(r'\}[^}]*$', '}', text)

            return text.strip()
        except Exception as e:
            self.logger.debug(f"清理JSON响应时出错: {e}")
            return text.strip()

    def _extract_json_from_response(self, content: str) -> Optional[dict]:
        """从LLM响应中提取JSON对象 - 多层解析策略"""
        try:
            # 第1层：直接解析（最快，适用于格式正确的响应）
            try:
                result = json.loads(content.strip())
                self.logger.debug("[JSON解析] 第1层直接解析成功")
                return result
            except json.JSONDecodeError:
                pass

            # 第2层：清理常见格式问题后解析
            cleaned_content = self._clean_json_response(content)
            try:
                result = json.loads(cleaned_content)
                self.logger.debug("[JSON解析] 第2层清理后解析成功")
                return result
            except json.JSONDecodeError:
                pass

            # 第3层：使用正则表达式提取（最后手段）
            json_patterns = [
                r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 处理简单嵌套
                r'\{(?:[^{}]|(?:\{[^{}]*\}))*\}',    # 处理更复杂嵌套
                r'\{.*?\}(?=\s*$)',                  # 匹配到字符串末尾的JSON
                r'\{[^}]*\}',                        # 最简单的匹配（原有逻辑）
            ]

            for i, pattern in enumerate(json_patterns):
                matches = re.findall(pattern, content, re.DOTALL)
                for match in matches:
                    try:
                        result = json.loads(match.strip())
                        self.logger.debug(f"[JSON解析] 第3层正则提取成功 (模式{i+1}): {match[:100]}...")
                        return result
                    except json.JSONDecodeError:
                        continue

            # 所有方法都失败
            self.logger.warning(f"[JSON解析] 所有解析方法都失败，响应内容: {content[:200]}...")
            return None

        except Exception as e:
            self.logger.error(f"[JSON解析] 提取过程中发生异常: {e}")
            return None

    # 加载决策规则
    def _load_decision_rules(self) -> Dict[str, Dict[str, Any]]:
        """加载决策规则"""
        # 基于状态和意图的决策规则
        rules = {
            # IDLE状态的决策规则
            "IDLE": {
                # 问候
                "greeting": {
                    "action": ActionType.SEND_GREETING.value,
                    "priority": 1,
                    "response_template": "greeting.responses",
                    "next_state": ConversationState.IDLE
                },
                # 自我介绍
                "ask_introduction": {
                    "action": "provide_self_introduction",
                    "priority": 2,
                    "response_template": "introduction.full",
                    "next_state": ConversationState.IDLE
                },
                # 能力介绍
                "ask_capabilities": {
                    "action": "explain_capabilities",
                    "priority": 2,
                    "response_template": "capabilities.full",
                    "next_state": ConversationState.IDLE
                },
                # 知识库查询
                "search_knowledge_base": {
                    "action": "search_knowledge_base",
                    "priority": 2,
                    "response_template": "knowledge_base.search",
                    "next_state": ConversationState.IDLE
                },
                # 展示能力
                "ask_question": {
                    "action": "explain_capabilities",
                    "priority": 1,
                    "response_template": "capabilities.main",
                    "next_state": ConversationState.IDLE
                },
                # 业务需求
                "business_requirement": {
                    "action": ActionType.START_REQUIREMENT_COLLECTION.value,
                    "priority": 1,
                    "response_template": "requirement_collection.start",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 复合意图：知识库查询 + 业务需求
                "composite_knowledge_requirement": {
                    "action": "handle_composite_knowledge_requirement",
                    "priority": 1,  # 最高优先级
                    "response_template": "composite.knowledge_requirement",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 情感支持
                "emotional_support": {
                    "action": "provide_emotional_support",
                    "priority": 2,
                    "response_template": "emotional.support",
                    "next_state": ConversationState.IDLE
                },
                # 一般聊天
                "general_chat": {
                    "action": "respond_to_general_chat",
                    "priority": 1,
                    "response_template": "general.chat",
                    "next_state": ConversationState.IDLE
                },
                # 领域特定查询
                "domain_specific_query": {
                    "action": "handle_business_domain_query",
                    "priority": 2,
                    "response_template": "domain.specific_query",
                    "next_state": ConversationState.IDLE
                },
                # 流程查询
                "process_query": {
                    "action": "explain_capabilities",
                    "priority": 2,
                    "response_template": "process.explanation",
                    "next_state": ConversationState.IDLE
                },
                # 系统能力查询
                "system_capability_query": {
                    "action": "explain_capabilities",
                    "priority": 2,
                    "response_template": "capabilities.detailed",
                    "next_state": ConversationState.IDLE
                },
                # 用户反馈
                "feedback": {
                    "action": "acknowledge_and_redirect",
                    "priority": 1,
                    "response_template": "feedback.acknowledgment",
                    "next_state": ConversationState.IDLE
                },
                # 澄清请求
                "request_clarification": {
                    "action": "clarify_intent",
                    "priority": 2,
                    "response_template": "clarification.request",
                    "next_state": ConversationState.IDLE
                },
                # 默认策略 - 改为澄清意图而不是直接进入需求采集
                "default": {
                    "action": "clarify_intent",
                    "priority": 0,
                    "response_template": "system.clarify_intent",
                    "next_state": ConversationState.IDLE
                }
            },
            
            # COLLECTING_INFO状态的决策规则
            "COLLECTING_INFO": {
                # 处理用户回答 - 核心业务流程
                "process_answer": {
                    "action": "process_answer_and_ask_next",
                    "priority": 3,
                    "response_template": "requirement_collection.continue",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 请求澄清
                "request_clarification": {
                    "action": "request_clarification",
                    "priority": 2,
                    "response_template": "requirement_collection.clarify",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 提供信息
                "provide_information": {
                    "action": "process_answer_and_ask_next",  # 修改为正确的action
                    "priority": 1,
                    "response_template": "requirement_collection.continue",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 确认
                "confirm": {
                    "action": ActionType.START_DOCUMENT_GENERATION.value,
                    "priority": 2,
                    "response_template": "requirement_collection.confirmed",
                    "next_state": ConversationState.DOCUMENTING
                },
                # 搜索知识库 - 在需求收集过程中的知识库查询
                "search_knowledge_base": {
                    "action": "search_knowledge_base",
                    "priority": 2,
                    "response_template": "knowledge.search_result",
                    "next_state": ConversationState.COLLECTING_INFO  # 回答后继续收集需求
                },
                # 询问问题 - 用户在需求收集过程中的提问
                "ask_question": {
                    "action": "provide_guidance_and_continue",
                    "priority": 2,
                    "response_template": "guidance.answer_and_continue",
                    "next_state": ConversationState.COLLECTING_INFO  # 回答后继续收集需求
                },
                # 复合意图：知识库查询 + 业务需求 - 在需求收集过程中也支持
                "composite_knowledge_requirement": {
                    "action": "handle_composite_knowledge_requirement",
                    "priority": 2,  # 高优先级，优先于默认的process_answer
                    "response_template": "composite.knowledge_requirement",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 重新开始
                "restart": {
                    "action": ActionType.RESTART_CONVERSATION.value,
                    "priority": 2,
                    "response_template": "system.welcome",
                    "next_state": ConversationState.IDLE
                },
                # 默认策略
                "default": {
                    "action": "process_answer_and_ask_next",  # 修改为正确的action
                    "priority": 0,
                    "response_template": "requirement_collection.continue",
                    "next_state": ConversationState.COLLECTING_INFO
                }
            },
            
            # DOCUMENTING状态的决策规则
            "DOCUMENTING": {
                # 确认
                "confirm": {
                    "action": ActionType.CONFIRM_DOCUMENT.value,
                    "priority": 2,
                    "response_template": "document.confirmed",
                    "next_state": ConversationState.COMPLETED
                },
                # 修改
                "modify": {
                    "action": ActionType.MODIFY_DOCUMENT.value,
                    "priority": 2,
                    "response_template": "document.modify",
                    "next_state": ConversationState.DOCUMENTING
                },
                # 重新开始
                "restart": {
                    "action": ActionType.RESTART_CONVERSATION.value,
                    "priority": 2,
                    "response_template": "system.welcome",
                    "next_state": ConversationState.IDLE
                },
                # 默认策略
                "default": {
                    "action": ActionType.MODIFY_DOCUMENT.value,
                    "priority": 0,
                    "response_template": "document.clarification",
                    "next_state": ConversationState.DOCUMENTING
                }
            },
            
            # COMPLETED状态的决策规则
            "COMPLETED": {
                # 业务需求
                "business_requirement": {
                    "action": ActionType.START_REQUIREMENT_COLLECTION.value,
                    "priority": 1,
                    "response_template": "requirement_collection.new_start",
                    "next_state": ConversationState.COLLECTING_INFO
                },
                # 问候
                "greeting": {
                    "action": ActionType.SEND_GREETING.value,
                    "priority": 1,
                    "response_template": "greeting.responses",
                    "next_state": ConversationState.IDLE
                },
                # 默认策略
                "default": {
                    "action": ActionType.SEND_GREETING.value,
                    "priority": 0,
                    "response_template": "system.new_requirement_prompt",
                    "next_state": ConversationState.IDLE
                }
            },

            # PROCESSING_INTENT状态的决策规则（过渡状态）
            "PROCESSING_INTENT": {
                # 默认策略
                "default": {
                    "action": ActionType.HANDLE_UNKNOWN.value,
                    "priority": 0,
                    "response_template": "system.processing",
                    "next_state": ConversationState.IDLE
                }
            }
        }
        
        return rules
    
    async def analyze(self, message: str, context: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        分析用户消息并做出决策

        使用三层意图识别系统：
        1. 关键词匹配（高性能，优先级最高）
        2. 语义匹配（中等性能，处理同义词）
        3. LLM识别（低性能，处理复杂意图）

        结合当前对话状态进行决策路由，确保状态一致性。

        Args:
            message: 用户输入的消息文本
            context: 可选的上下文信息列表，包含会话状态等

        Returns:
            Dict[str, Any]: 决策结果，包含推荐动作、置信度、意图等信息
        """
        try:
            # 初始化状态和意图
            current_state = ConversationState.IDLE
            final_intent = "unknown"
            emotion = "neutral"
            confidence = self.config.get_threshold("confidence.minimum", 0.0)

            # 如果上下文存在，则尝试从中提取状态信息
            if context and len(context) > 0:
                ctx = context[0]
                state_name = ctx.get("current_state", "IDLE")
                try:
                    current_state = ConversationState[state_name]
                except KeyError:
                    current_state = ConversationState.IDLE

                # Layer 1: 关键词识别 - 收集所有匹配的意图
                detected_intents = self._extract_intent_from_message(message, context)
                self.logger.info(f"[复合意图识别] 检测到意图: {detected_intents}")

                # 初始化情绪和置信度
                emotion = "neutral"
                confidence = self.config.get_threshold("confidence.high", 0.8)

                # 优先级裁决和复合意图处理
                if detected_intents == ["needs_llm_analysis"]:
                    # 需要LLM分析
                    self.logger.info(f"[复合意图识别] 关键词无匹配，启用LLM意图识别: '{message}'")
                    llm_result = await self._llm_intent_recognition(message, context)
                    if isinstance(llm_result, dict):
                        final_intent = llm_result.get("intent", "general_chat")
                        sub_intent = llm_result.get("sub_intent", "")
                        emotion = llm_result.get("emotion", "neutral")
                        confidence = llm_result.get("confidence", self.config.get_threshold("confidence.high", 0.8))
                    else:
                        # 向后兼容，如果返回的是字符串
                        final_intent = llm_result
                        sub_intent = ""
                        emotion = "neutral"

                    sub_intent_info = f", sub_intent={sub_intent}" if sub_intent else ""
                    self.logger.info(f"[复合意图识别] LLM意图识别结果: intent={final_intent}{sub_intent_info}, emotion={emotion}")
                else:
                    # 复合意图处理：检查是否同时包含知识库查询和业务需求
                    final_intent = self._handle_composite_intent(detected_intents, message)
                    # 关键词识别时，尝试简单的情绪推断
                    emotion = self._infer_emotion_from_keywords(message, final_intent)

                    # 如果是业务需求，尝试通过LLM获取子意图
                    sub_intent = ""
                    if final_intent == "business_requirement":
                        try:
                            self.logger.info(f"[子意图识别] 检测到业务需求，调用LLM获取子意图")
                            llm_result = await self._llm_intent_recognition(message, context)
                            if isinstance(llm_result, dict):
                                sub_intent = llm_result.get("sub_intent", "")
                                # 也可以更新情感信息
                                llm_emotion = llm_result.get("emotion", "")
                                if llm_emotion:
                                    emotion = llm_emotion
                        except Exception as e:
                            self.logger.warning(f"获取子意图失败: {e}")

                    sub_intent_info = f", sub_intent={sub_intent}" if sub_intent else ""
                    self.logger.info(f"[复合意图处理] 从 {detected_intents} 中选择: {final_intent}{sub_intent_info}, 推断情绪: {emotion}")

            # 调用核心决策方法
            decision_result = self.make_decision(final_intent, current_state,
                                               context[0] if context else None)

            # 转换为兼容格式
            result = {
                "decision": {
                    "action": decision_result.action,
                    "priority": decision_result.priority,
                    "response_template": decision_result.response_template,
                    "next_state": decision_result.next_state.value if decision_result.next_state else None
                },
                "intent": final_intent,
                "emotion": emotion,  # 添加情绪信息
                "confidence": confidence,
                "metadata": decision_result.metadata
            }

            # 如果有子意图，添加到结果中
            if 'sub_intent' in locals() and sub_intent:
                result["sub_intent"] = sub_intent

            return result

        except Exception as e:
            self.logger.error(f"分析失败: {e}", exc_info=True)
            return {
                "decision": {
                    "action": "handle_unknown_situation",
                    "priority": 0,
                    "response_template": "system.error"
                },
                "intent": "unknown",
                "confidence": self.config.get_threshold("confidence.minimum", 0.0),
                "metadata": {"error": str(e)}
            }

    def _handle_composite_intent(self, detected_intents: List[str], message: str) -> str:
        """
        处理复合意图：当同时检测到多个意图时的智能选择

        特殊处理规则：
        1. 如果同时包含知识库查询和业务需求，优先选择业务需求（引导到业务流程）
        2. 如果知识库查询是纯粹的功能询问，则选择知识库查询
        3. 其他情况按原有优先级规则处理
        """
        # 检查是否同时包含知识库查询和业务需求相关意图
        has_knowledge_query = "search_knowledge_base" in detected_intents
        has_business_intent = any(intent in detected_intents for intent in [
            "business_requirement", "ask_question", "provide_information"
        ])

        if has_knowledge_query and has_business_intent:
            # 🔧 简化逻辑：完全基于已识别的意图，避免硬编码关键词
            # 如果同时检测到知识库查询和业务需求意图，则判断为复合意图

            # 简单的启发式规则：检查消息结构
            message_lower = message.lower()

            # 检查是否包含明确的分隔符或连接词，表明这是复合表达
            has_separator = any(sep in message for sep in ['。', '.', '，', ',', '；', ';'])
            has_connector = any(conn in message_lower for conn in ['还有', '另外', '以及', '同时'])

            # 检查是否包含明确的需求表达词
            has_requirement_expression = any(expr in message_lower for expr in [
                '我想', '我要', '我需要', '帮我', '想要', '希望'
            ])

            # 如果有分隔符、连接词或明确需求表达，认为是复合意图
            if has_separator or has_connector or has_requirement_expression:
                self.logger.info(f"[复合意图处理] 检测到复合意图：知识库查询 + 业务需求，使用复合处理")
                return "composite_knowledge_requirement"

        # 其他情况按原有优先级规则处理
        return self._select_intent_by_priority(detected_intents)

    # 根据优先级选择最终意图
    def _select_intent_by_priority(self, detected_intents: List[str]) -> str:
        """根据优先级规则选择最终意图"""
        for priority_intent in self.intent_priority:
            if priority_intent in detected_intents:
                return priority_intent

        # 如果没有任何匹配的优先级意图，返回业务需求作为兜底
        return "business_requirement"


    # Layer 1: 关键词识别
    def _extract_intent_from_message(self, message: str, context: Optional[List[Dict[str, Any]]] = None) -> List[str]:
        """Layer 1: 关键词识别 - 收集所有匹配的意图"""
        message_lower = message.lower()
        detected_intents = []

        # 🔧 [关键词重构] 知识库查询 - 使用统一关键词配置
        knowledge_base_detected = False
        if self.keywords_loader:
            # 使用统一关键词配置
            try:
                all_kb_keywords = self.keywords_loader.get_all_knowledge_base_keywords_flat()
                knowledge_base_detected = any(keyword in message_lower for keyword in all_kb_keywords)

                if knowledge_base_detected:
                    # 记录匹配的关键词用于调试
                    matched_keywords = [kw for kw in all_kb_keywords if kw in message_lower]
                    self.logger.debug(f"知识库查询关键词匹配: {matched_keywords}")

            except Exception as e:
                self.logger.error(f"统一关键词匹配失败，回退到硬编码: {e}")
                knowledge_base_detected = False

        # 备用硬编码关键词（向后兼容）
        if not knowledge_base_detected:
            legacy_keywords = [
                # 现有的关键词
                "如何注册", "怎么注册", "注册流程", "账号注册",
                "如何使用", "怎么使用", "使用方法", "操作步骤",
                "什么是", "介绍一下", "功能说明", "如何操作",
                "支持哪些", "有哪些功能", "提供什么", "能用什么",
                "哪些", "都有哪些", "包括哪些", "具体哪些",
                "登录方法", "密码重置", "忘记密码",
                "功能介绍", "产品说明", "服务介绍",
                "收费标准", "价格说明", "套餐介绍",
                "发布用工", "发布职位", "发布招聘", "发布工作", "发布需求",
                # 可以添加的新关键词
                "技术支持", "客服联系", "帮助文档",
                "常见问题", "FAQ", "问题解答", "疑难解答",
                "由己平台", "由己平台介绍", "什么是由己平台",
                "由己平台功能", "由己平台怎么用", "介绍一下由己平台",
                # 添加自我介绍相关关键词，让其优先使用知识库检索
                "介绍一下自己", "介绍下自己", "你是谁", "你是什么",
                "自我介绍", "介绍自己", "请介绍自己", "你好，请介绍自己"
            ]
            knowledge_base_detected = any(phrase in message_lower for phrase in legacy_keywords)
            if knowledge_base_detected:
                self.logger.debug("使用备用硬编码关键词匹配知识库查询")

        if knowledge_base_detected:
            detected_intents.append("search_knowledge_base")

        # 业务需求 - 扩展关键词以识别隐含需求
        business_keywords = [
            # 明确的需求表达
            "我想做", "我需要", "帮我做", "我要", "想要", "开发", "设计", "制作", "创建",
            "策划", "建设", "搭建", "实现", "完成", "做一个", "弄一个",
            # 支持类需求表达
            "想先支持", "希望支持", "需要支持", "要支持", "支持", "集成", "接入",
            "想先", "希望先", "计划", "准备", "打算",
            # 隐含的业务需求（功能描述）
            "产品展示", "用户注册", "在线支付", "支付功能", "注册功能", "展示功能",
            "购物车", "订单管理", "用户管理", "商品管理", "内容管理",
            "网站功能", "app功能", "系统功能", "平台功能",
            "营销活动", "推广方案", "宣传策略", "品牌设计",
            # 支付相关需求
            "apple pay", "google pay", "微信支付", "支付宝", "银联支付"
        ]
        if any(phrase in message_lower for phrase in business_keywords):
            detected_intents.append("business_requirement")

        # 问候语
        if any(word in message_lower for word in ["你好", "hello", "hi", "您好"]):
            detected_intents.append("greeting")

        # 自我介绍请求
        if any(phrase in message_lower for phrase in ["介绍一下自己", "介绍下自己", "你是谁", "你是什么", "自我介绍", "介绍自己"]):
            detected_intents.append("ask_introduction")

        # 能力询问
        if any(phrase in message_lower for phrase in ["你能做什么", "有什么功能", "能帮我什么", "你的能力", "功能介绍"]):
            detected_intents.append("ask_capabilities")

        # 获取当前状态
        current_state = "IDLE"
        if context and len(context) > 0:
            current_state = context[0].get("current_state", "IDLE")

        # 确认 - 只在DOCUMENTING状态下有效
        confirm_keywords = ["是的", "对", "确认", "好的", "可以"]
        confirm_matched = any(word in message_lower for word in confirm_keywords)

        if current_state == "DOCUMENTING" and confirm_matched:
            detected_intents.append("confirm")

        # 重新开始 - 只在DOCUMENTING状态下有效
        if current_state == "DOCUMENTING" and any(phrase in message_lower for phrase in ["重新开始", "重来", "全部重来", "重新", "新需求", "重新来"]):
            detected_intents.append("restart")

        # 修改 - 只在DOCUMENTING状态下有效
        if current_state == "DOCUMENTING" and any(word in message_lower for word in ["修改", "改", "更改", "调整"]):
            detected_intents.append("modify")

        # 情感支持 - 扩展关键词列表
        emotional_keywords = [
            "心情不好", "安慰我", "难过", "沮丧", "不开心", "郁闷",
            "气死我了", "气死了", "烦死了", "烦死我了", "受不了", "要疯了",
            "崩溃", "抓狂", "痛苦", "悲伤", "愤怒", "生气", "不爽", "烦躁"
        ]
        if any(phrase in message_lower for phrase in emotional_keywords):
            detected_intents.append("emotional_support")

        # 🚨 状态感知规则：根据当前状态强制调整意图识别结果
        # 这是修复"一般开发这样的app需要多少钱"被错误识别为business_requirement的关键逻辑
        if current_state == "COLLECTING_INFO":
            # 在COLLECTING_INFO状态下，除了明确的重新开始和询问问题含义，其他都应该识别为process_answer
            restart_keywords = ["重新开始", "重来", "全部重来", "重新", "新需求", "新聊天"]
            clarification_keywords = ["这个问题是什么意思", "你在问什么", "什么意思", "不太明白"]

            is_restart = any(keyword in message_lower for keyword in restart_keywords)
            is_clarification = any(keyword in message_lower for keyword in clarification_keywords)

            # 🔧 复合意图识别修复：检测是否包含知识库查询
            # 修复问题："用户注册登录、商品展示、在线支付.你们平台支持哪些支付？"应该识别为复合意图
            knowledge_query_indicators = [
                "支持哪些", "有哪些", "包括哪些", "都有哪些", "具体哪些",
                "什么是", "如何", "怎么", "怎样", "如何使用", "怎么使用",
                "？", "?", "吗", "呢", "多少", "价格", "费用", "收费"
            ]
            has_knowledge_query = any(indicator in message_lower for indicator in knowledge_query_indicators)

            if not is_restart and not is_clarification:
                if has_knowledge_query:
                    # 包含知识查询，允许复合意图处理逻辑正常工作
                    self.logger.info(f"[状态感知] COLLECTING_INFO状态下检测到知识查询，允许复合意图处理: '{message}'")
                    # 不强制覆盖，让复合意图处理逻辑在后续步骤中执行
                else:
                    # 纯粹的回答，强制识别为process_answer
                    self.logger.info(f"[状态感知] COLLECTING_INFO状态下强制识别为process_answer: '{message}'")
                    return ["process_answer"]

        # 如果没有匹配到任何意图，返回需要LLM分析
        if not detected_intents:
            return ["needs_llm_analysis"]

        return detected_intents

    def _infer_emotion_from_keywords(self, message: str, intent: str) -> str:
        """从关键词推断用户情绪"""
        message_lower = message.lower()

        # 负面情绪关键词
        negative_keywords = [
            "气死", "烦死", "郁闷", "沮丧", "难过", "不开心", "讨厌", "恶心",
            "糟糕", "倒霉", "失望", "绝望", "痛苦", "悲伤", "愤怒", "生气",
            "不爽", "烦躁", "抓狂", "崩溃", "受不了", "要疯了"
        ]

        # 焦虑情绪关键词
        anxious_keywords = [
            "担心", "紧张", "害怕", "恐惧", "不安", "焦虑", "忧虑", "慌张",
            "着急", "急死", "怎么办", "完了", "糟了", "坏了", "紧急"
        ]

        # 困惑情绪关键词
        confused_keywords = [
            "不懂", "不明白", "搞不清", "糊涂", "迷茫", "困惑", "不知道",
            "怎么回事", "什么意思", "看不懂", "理解不了", "搞不懂"
        ]

        # 积极情绪关键词
        positive_keywords = [
            "开心", "高兴", "兴奋", "激动", "满意", "棒", "好的", "太好了",
            "完美", "优秀", "赞", "喜欢", "爱", "感谢", "谢谢"
        ]

        # 检查负面情绪
        if any(keyword in message_lower for keyword in negative_keywords):
            return "negative"

        # 检查焦虑情绪
        if any(keyword in message_lower for keyword in anxious_keywords):
            return "anxious"

        # 检查困惑情绪
        if any(keyword in message_lower for keyword in confused_keywords):
            return "confused"

        # 检查积极情绪
        if any(keyword in message_lower for keyword in positive_keywords):
            return "positive"

        # 根据意图推断情绪
        if intent == "emotional_support":
            return "negative"  # 寻求情感支持通常表示负面情绪
        elif intent == "ask_question" and any(word in message_lower for word in ["怎么", "如何", "什么"]):
            return "confused"  # 询问问题可能表示困惑

        return "neutral"

    # Layer 2: LLM意图识别
    async def _llm_intent_recognition(self, message: str, context: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Layer 2: LLM意图识别 - 使用模板系统，支持状态感知"""
        try:
            # 使用已注入的服务而不是延迟导入
            llm_service = self.llm_service
            prompt_loader = self.prompt_loader

            # 准备模板参数
            current_state = "IDLE"
            conversation_history = ""

            if context and len(context) > 0:
                ctx = context[0]
                current_state = ctx.get("current_state", "IDLE")

                # 构建对话历史（简化版本）
                if "conversation_history" in ctx:
                    history = ctx["conversation_history"]
                    if isinstance(history, list) and len(history) > 0:
                        # 取最近3轮对话作为上下文
                        recent_history = history[-6:] if len(history) > 6 else history

                        # 处理不同格式的历史记录
                        formatted_messages = []
                        for msg in recent_history:
                            if isinstance(msg, dict):
                                # 数据库格式：包含role和content字段
                                role = msg.get("role", "unknown")
                                content = msg.get("content", "")
                                role_display = "用户" if role == "user" else "AI"
                                formatted_messages.append(f"{role_display}: {content}")
                            elif isinstance(msg, str):
                                # 简单字符串格式（向后兼容）
                                formatted_messages.append(msg)

                        conversation_history = "\n".join(formatted_messages)

            # 使用模板加载提示词
            try:
                prompt = prompt_loader.load_prompt(
                    "intent_recognition",
                    {
                        "user_input": message,
                        "full_conversation": conversation_history,
                        "current_state": current_state
                    }
                )
                self.logger.info(f"[模板系统] 成功加载意图识别模板，状态: {current_state}")

            except Exception as e:
                self.logger.warning(f"加载意图识别模板失败: {e}，使用备用提示词")
                # 备用硬编码提示词 - 简化版本
                prompt = f"""
请分析以下用户输入的意图，并返回JSON格式结果。

用户输入: {message}
当前状态: {current_state}

请返回JSON格式：
{{"intent": "意图类型", "confidence": 0.9}}

可能的意图类型：
- greeting: 问候语
- business_requirement: 业务需求
- emotional_support: 情感支持
- ask_question: 询问问题
- general_chat: 一般聊天
- provide_information: 提供信息
- unknown: 无法识别

只返回JSON，不要其他内容。
"""

            # 调用LLM
            response = await llm_service.call_llm(
                messages=[{"role": "user", "content": prompt}],
                agent_name="simplified_decision_engine",
                scenario="intent_recognition"
            )

            # 解析响应
            content = response.get("content", "").strip()
            self.logger.debug(f"[LLM意图识别] 原始响应: {content}")

            # 使用多层解析策略提取JSON
            result = self._extract_json_from_response(content)

            if result:
                intent = result.get("intent", "").lower()
                emotion = result.get("emotion", "neutral")
                confidence = result.get("confidence", self.config.get_threshold("confidence.minimum", 0.0))

                self.logger.info(f"[LLM意图识别] JSON解析成功: intent={intent}, emotion={emotion}, confidence={confidence}")

                # 🔄 [意图统一化] 验证意图有效性 - 使用配置驱动
                if self.intent_manager:
                    valid_intents = self.intent_manager.get_valid_intents()
                else:
                    # 备用硬编码列表（当配置加载失败时使用）
                    valid_intents = [
                        "greeting", "business_requirement", "emotional_support",
                        "ask_question", "general_chat", "provide_information", "unknown",
                        "confirm", "restart", "modify",  # 关键词匹配支持的意图
                        "process_answer", "request_clarification",  # 核心业务流程意图
                        "domain_specific_query", "process_query", "system_capability_query", "feedback"  # 模板中定义的其他意图
                    ]

                if intent in valid_intents:
                    # 提取子意图
                    sub_intent = result.get("sub_intent", "")

                    return {
                        "intent": intent,
                        "sub_intent": sub_intent,
                        "emotion": emotion,
                        "confidence": confidence
                    }
                else:
                    self.logger.warning(f"[LLM意图识别] 解析出的意图 '{intent}' 无效")
            else:
                self.logger.debug("[LLM意图识别] JSON解析失败，尝试文本匹配")

            # 🔄 [意图统一化] 回退到文本匹配（兼容备用提示词）
            content_lower = content.lower()
            if self.intent_manager:
                valid_intents = self.intent_manager.get_valid_intents()
            else:
                # 备用硬编码列表（当配置加载失败时使用）
                valid_intents = [
                    "greeting", "business_requirement", "emotional_support",
                    "ask_question", "general_chat", "provide_information", "unknown",
                    "confirm", "restart", "modify",  # 关键词匹配支持的意图
                    "process_answer", "request_clarification",  # 核心业务流程意图
                    "domain_specific_query", "process_query", "system_capability_query", "feedback"  # 模板中定义的其他意图
                ]

            for intent in valid_intents:
                if intent in content_lower:
                    return {
                        "intent": intent,
                        "emotion": "neutral",  # 文本匹配无法识别情绪，使用默认值
                        "confidence": self.config.get_threshold("confidence.low", 0.6)
                    }

            # 如果无法识别，返回通用聊天
            self.logger.warning(f"[LLM意图识别] 无法识别意图，返回默认值: general_chat")

            return {
                "intent": "general_chat",
                "emotion": "neutral",
                "confidence": self.config.get_threshold("confidence.low", 0.3)
            }

        except Exception as e:
            self.logger.error(f"LLM意图识别失败: {e}", exc_info=True)
            # 回退到通用聊天
            return {
                "intent": "general_chat",
                "emotion": "neutral",
                "confidence": self.config.get_threshold("confidence.minimum", 0.0)
            }

    # 支持子意图的策略获取方法
    def get_strategy(self, intent: str, emotion: str, context: Dict[str, Any],
                    sub_intent: Optional[str] = None) -> Dict[str, Any]:
        """
        获取策略配置，支持子意图处理

        Args:
            intent: 主意图
            emotion: 情感
            context: 上下文信息
            sub_intent: 子意图（可选）

        Returns:
            Dict[str, Any]: 策略配置
        """
        try:
            # 从context中获取当前状态
            current_state_str = context.get("current_state", "IDLE")
            try:
                current_state = ConversationState[current_state_str]
            except KeyError:
                current_state = ConversationState.IDLE

            # 调用核心决策方法
            decision_result = self.make_decision(intent, current_state, context)

            # 转换为策略格式
            strategy = {
                "action": decision_result.action,
                "priority": decision_result.priority,
                "response_template": decision_result.response_template,
                "next_state": decision_result.next_state.value if decision_result.next_state else None,
                "prompt_instruction": f"处理{intent}意图"
            }

            # 如果有子意图，添加到metadata中
            if sub_intent:
                strategy["sub_intent"] = sub_intent
                strategy["prompt_instruction"] = f"处理{intent}.{sub_intent}意图"

            sub_intent_info = f", sub_intent={sub_intent}" if sub_intent else ""
            self.logger.info(f"[策略获取] intent={intent}, emotion={emotion}{sub_intent_info} -> action={strategy['action']}")

            return strategy

        except Exception as e:
            self.logger.error(f"获取策略失败: {e}", exc_info=True)
            return {
                "action": "handle_unknown_situation",
                "priority": 0,
                "response_template": "system.error",
                "next_state": "IDLE",
                "prompt_instruction": "处理未知情况"
            }

    # 核心决策方法
    def make_decision(self, intent: str, current_state: ConversationState,
                     context: Optional[Dict[str, Any]] = None) -> DecisionResult:
        """
        核心决策方法

        Args:
            intent: 识别出的意图
            current_state: 当前对话状态
            context: 上下文信息

        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 获取状态规则
            state_rules = self._decision_rules.get(current_state.value, {})
            
            # 匹配意图规则
            rule = state_rules.get(intent, state_rules.get("default"))
            
            if not rule:
                # 如果没有找到规则，使用通用默认规则
                rule = self._get_fallback_rule(intent, current_state)
            
            # 返回决策结果
            result = DecisionResult(
                action=rule["action"],
                priority=rule["priority"],
                response_template=rule["response_template"],
                next_state=rule.get("next_state"),
                metadata={
                    "intent": intent,
                    "current_state": current_state.value,
                    "rule_matched": intent in state_rules,
                    "context": context or {}
                }
            )
            
            self.logger.debug(f"决策完成: {intent} + {current_state.value} -> {result.action}")
            return result
            
        except Exception as e:
            self.logger.error(f"决策失败: {e}", exc_info=True)
            return self._get_error_fallback_result(intent, current_state)
    
    # 获取回退规则
    def _get_fallback_rule(self, intent: str, current_state: ConversationState) -> Dict[str, Any]:
        """获取回退规则"""
        # 注意：intent参数保留用于未来可能的意图特定回退逻辑
        _ = intent  # 明确标记参数已知但未使用

        # 基于状态的智能回退
        if current_state == ConversationState.IDLE:
            return {
                "action": ActionType.START_REQUIREMENT_COLLECTION.value,
                "priority": 0,
                "response_template": "requirement_collection.start",
                "next_state": ConversationState.COLLECTING_INFO
            }
        elif current_state == ConversationState.COLLECTING_INFO:
            return {
                "action": ActionType.COLLECT_INFORMATION.value,
                "priority": 0,
                "response_template": "requirement_collection.continue",
                "next_state": ConversationState.COLLECTING_INFO
            }
        elif current_state == ConversationState.DOCUMENTING:
            return {
                "action": ActionType.MODIFY_DOCUMENT.value,
                "priority": 0,
                "response_template": "document.clarification",
                "next_state": ConversationState.DOCUMENTING
            }
        else:  # COMPLETED
            return {
                "action": ActionType.SEND_GREETING.value,
                "priority": 0,
                "response_template": "system.new_requirement_prompt",
                "next_state": ConversationState.IDLE
            }
    
    # 获取错误回退结果
    def _get_error_fallback_result(self, intent: str, current_state: ConversationState) -> DecisionResult:
        """获取错误回退结果"""
        return DecisionResult(
            action=ActionType.HANDLE_UNKNOWN.value,
            priority=0,
            response_template="system.error",
            next_state=current_state,  # 保持当前状态
            metadata={
                "error": True,
                "intent": intent,
                "current_state": current_state.value
            }
        )
    
    # 获取响应模板
    def get_response_template(self, template_path: str) -> str:
        """获取响应模板"""
        try:
            # 解析模板路径，例如 "greeting.responses"
            parts = template_path.split('.')
            if len(parts) == 2:
                category, template_type = parts
                return self.config.get_template(category, template_type)
            else:
                # 简单模板路径
                return self.config.get_template(template_path)
        except Exception as e:
            self.logger.error(f"获取响应模板失败: {template_path} - {e}")
            return "抱歉，系统遇到了一些问题，请稍后再试。"
    
    # 获取可用动作
    def get_available_actions(self) -> List[str]:
        """获取可用的动作类型"""
        return [action.value for action in ActionType]
    
    # 决策规则摘要
    def get_decision_rules_summary(self) -> Dict[str, List[str]]:
        """获取决策规则摘要"""
        summary = {}
        for state, rules in self._decision_rules.items():
            summary[state] = list(rules.keys())
        return summary
    
    # 测试复合意图识别功能
    def test_compound_intent_recognition(self) -> Dict[str, Any]:
        """测试复合意图识别功能"""
        test_cases = [
            ("你好，我想设计一张海报", ["greeting", "business_requirement"], "business_requirement"),
            ("我开发的app需要有支付功能，你们平台支持哪些支付", ["business_requirement", "search_knowledge_base"], "search_knowledge_base"),
            ("你好，请问如何注册账号？", ["greeting", "search_knowledge_base"], "search_knowledge_base"),
            ("我需要制作一个网站", ["business_requirement"], "business_requirement"),
            ("你好", ["greeting"], "greeting"),
            ("介绍一下你们的功能", ["ask_capabilities"], "ask_capabilities"),
            ("这个方案可以，同意", ["confirm"], "confirm"),
            ("重新开始", ["restart"], "restart"),
            ("修改一下", ["modify"], "modify"),
            ("我不开心", ["emotional_support"], "emotional_support"),
            ("随便聊聊", [], "business_requirement")  # 兜底
        ]
        
        results = {
            "total_tests": len(test_cases),
            "passed": 0,
            "failed": 0,
            "details": []
        }
        
        for message, expected_detected, expected_final in test_cases:
            # 测试意图收集
            detected = self._extract_intent_from_message(message)
            if detected == ["needs_llm_analysis"] and not expected_detected:
                detected = ["business_requirement"]  # 处理兜底情况
            
            # 测试优先级裁决
            final = self._select_intent_by_priority(detected)
            
            passed = (set(detected) == set(expected_detected) or 
                     (detected == ["needs_llm_analysis"] and expected_final == "business_requirement")) and final == expected_final
            
            test_result = {
                "message": message,
                "detected": detected,
                "expected_detected": expected_detected,
                "final": final,
                "expected_final": expected_final,
                "passed": passed
            }
            
            if passed:
                results["passed"] += 1
            else:
                results["failed"] += 1
            
            results["details"].append(test_result)
        
        return results

    # 验证决策规则
    def validate_decision_rules(self) -> Dict[str, Any]:
        """验证决策规则的完整性和优先级规则"""
        validation_result = {
            "valid": True,
            "issues": [],
            "statistics": {}
        }
        
        # 检查每个状态是否有默认规则
        for state in ConversationState:
            state_rules = self._decision_rules.get(state.value, {})
            if "default" not in state_rules:
                validation_result["issues"].append(f"状态 {state.value} 缺少默认规则")
                validation_result["valid"] = False
        
        # 统计信息
        validation_result["statistics"] = {
            "total_states": len(self._decision_rules),
            "total_rules": sum(len(rules) for rules in self._decision_rules.values()),
            "states_with_default": sum(1 for rules in self._decision_rules.values() if "default" in rules),
            "intent_priority_rules": len(self.intent_priority),
            "priority_order": self.intent_priority
        }
        
        return validation_result


# 创建全局实例
_decision_engine_instance = None


def get_simplified_decision_engine() -> SimplifiedDecisionEngine:
   """获取简化决策引擎实例"""
   global _decision_engine_instance
   if _decision_engine_instance is None:
        _decision_engine_instance = SimplifiedDecisionEngine()
   return _decision_engine_instance


if __name__ == "__main__":
    # 测试简化决策引擎
    print("🧪 测试简化决策引擎...")
    
    engine = get_simplified_decision_engine()
    
    # 测试决策规则验证
    validation = engine.validate_decision_rules()
    print(f"决策规则验证: {validation}")
    
    # 测试各种决策场景
    test_cases = [
        ("greeting", ConversationState.IDLE),
        ("business_requirement", ConversationState.IDLE),
        ("provide_information", ConversationState.COLLECTING_INFO),
        ("confirm", ConversationState.COLLECTING_INFO),
        ("confirm", ConversationState.DOCUMENTING),
        ("unknown_intent", ConversationState.IDLE)
    ]
    
    for intent, state in test_cases:
        result = engine.make_decision(intent, state)
        print(f"决策测试: {intent} + {state.value} -> {result.action} (优先级: {result.priority})")
    
    # 测试可用动作
    actions = engine.get_available_actions()
    print(f"可用动作: {len(actions)} 个")
    
    # 测试决策规则摘要
    summary = engine.get_decision_rules_summary()
    print(f"决策规则摘要: {summary}")
    
    print("🎉 简化决策引擎测试完成!")
    print("🎉 简化决策引擎测试完成!")
