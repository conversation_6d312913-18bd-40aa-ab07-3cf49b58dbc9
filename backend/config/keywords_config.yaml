# 统一关键词配置文件
# 这是所有知识库查询关键词的单一数据源
# 版本: 1.0
# 创建时间: 2025-08-08
# 说明: 整合了之前分散在8个不同位置的关键词定义
#
# 🔧 [关键词重构] 这是新的统一关键词配置系统
# 替代了以下旧配置中的重复关键词定义:
# - unified_config.yaml 中的 keyword_acceleration 和 keyword_rules
# - intent_definitions.yaml 中的示例关键词
# - 各个模块中的硬编码关键词列表

# 知识库查询关键词配置
knowledge_base_keywords:
  # 价格相关查询
  pricing:
    - "价格"
    - "费用"
    - "收费"
    - "套餐"
    - "计费"
    - "多少钱"
    - "收费标准"
    - "价格表"
    - "价格说明"
    - "付费"
    - "免费"
    - "成本"
    - "报价"
    - "预算"
    - "花费"
    - "开销"

  # 功能相关查询
  features:
    - "功能"
    - "特点"
    - "能力"
    - "支持"
    - "提供"
    - "包含"
    - "有什么"
    - "能做什么"
    - "功能列表"
    - "服务内容"
    - "特色"
    - "优势"
    - "亮点"

  # 使用方法查询
  usage:
    - "怎么用"
    - "如何使用"
    - "使用方法"
    - "操作步骤"
    - "使用说明"
    - "教程"
    - "指南"
    - "帮助"
    - "说明书"
    - "手册"

  # 注册相关查询
  registration:
    - "注册"
    - "开户"
    - "申请"
    - "账号"
    - "登录"
    - "注册流程"
    - "开通"
    - "激活"

  # 技术支持查询
  support:
    - "技术支持"
    - "客服"
    - "联系方式"
    - "售后"
    - "问题"
    - "故障"
    - "bug"
    - "错误"
    - "异常"

  # 产品信息查询
  product_info:
    - "产品介绍"
    - "产品说明"
    - "详细信息"
    - "规格"
    - "参数"
    - "配置"
    - "版本"
    - "更新"

# 系统能力查询关键词（用于关键词加速）
system_capability_keywords:
  general_inquiry:
    - "你能做什么"
    - "你会什么"
    - "你的功能"
    - "有什么功能"
    - "能帮我什么"
    - "你是什么"
    - "介绍一下"
    - "你好"
    - "hello"
    - "hi"

  greeting:
    - "你好"
    - "您好"
    - "hello"
    - "hi"
    - "早上好"
    - "下午好"
    - "晚上好"

# 意图识别关键词映射
intent_keywords:
  # 知识库查询意图的所有关键词（合并所有分类）
  search_knowledge_base:
    - "价格"
    - "费用"
    - "收费"
    - "套餐"
    - "计费"
    - "多少钱"
    - "收费标准"
    - "价格表"
    - "价格说明"
    - "功能"
    - "特点"
    - "能力"
    - "支持"
    - "有什么"
    - "能做什么"
    - "怎么用"
    - "如何使用"
    - "使用方法"
    - "注册"
    - "开户"
    - "申请"
    - "技术支持"
    - "客服"
    - "联系方式"
    - "产品介绍"
    - "产品说明"

# 配置元数据
metadata:
  version: "1.0"
  created_date: "2025-08-08"
  description: "统一关键词配置，整合了之前分散的关键词定义"
  migration_sources:
    - "backend/agents/strategies/knowledge_base_strategy.py"
    - "backend/agents/simplified_decision_engine.py"
    - "backend/agents/context_analyzer.py"
    - "backend/handlers/requirement_handler.py"
    - "backend/config/intent_definitions.yaml"
    - "backend/config/unified_config.yaml"
  
# 向后兼容性配置
compatibility:
  # 保持与现有代码的兼容性
  legacy_mapping:
    # knowledge_base_strategy.py 的分类映射
    pricing: "pricing"
    features: "features"
    usage: "usage"
    registration: "registration"
    support: "support"
    product_info: "product_info"
